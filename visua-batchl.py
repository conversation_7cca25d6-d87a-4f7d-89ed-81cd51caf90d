import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from matplotlib.ticker import Scalar<PERSON><PERSON>atter
from matplotlib import dates as mdates
from datetime import datetime
import re
from typing import List, Tuple, Optional
# ========== 中文字体配置 ==========
plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows系统字体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示异常

def load_prediction_data(prediction_csv_path: str = "原始结果.csv") -> pd.DataFrame:
    """加载预测结果数据"""
    try:
        return pd.read_csv(prediction_csv_path, encoding='utf-8-sig')
    except Exception as e:
        print(f"⚠️ 无法加载预测数据: {e}")
        return pd.DataFrame()

def parse_time_ranges(time_str: str) -> List[Tuple[datetime, datetime]]:
    """解析时间范围字符串"""
    if pd.isna(time_str) or time_str == "无预警":
        return []

    ranges = []
    # 匹配完整日期时间格式
    pattern1 = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})到(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
    matches1 = re.findall(pattern1, time_str)

    for start_str, end_str in matches1:
        try:
            start_time = datetime.strptime(start_str, '%Y-%m-%d %H:%M:%S')
            end_time = datetime.strptime(end_str, '%Y-%m-%d %H:%M:%S')
            ranges.append((start_time, end_time))
        except ValueError:
            continue

    # 匹配简化格式 (MMDD HH:MM)
    pattern2 = r'(\d{4}) (\d{2}:\d{2})到(\d{4}) (\d{2}:\d{2})'
    matches2 = re.findall(pattern2, time_str)

    for date1, time1, date2, time2 in matches2:
        try:
            # 假设年份为2025
            month1, day1 = date1[:2], date1[2:]
            month2, day2 = date2[:2], date2[2:]

            start_time = datetime.strptime(f'2025-{month1}-{day1} {time1}:00', '%Y-%m-%d %H:%M:%S')
            end_time = datetime.strptime(f'2025-{month2}-{day2} {time2}:00', '%Y-%m-%d %H:%M:%S')
            ranges.append((start_time, end_time))
        except ValueError:
            continue

    return ranges

def parse_actual_time(time_str: str) -> Optional[datetime]:
    """解析实际卡钻时间"""
    if pd.isna(time_str) or time_str == "不卡钻":
        return None

    # 匹配 YYYY/M/D H:M 格式
    pattern = r'(\d{4})/(\d{1,2})/(\d{1,2}) (\d{1,2}):(\d{1,2})'
    match = re.search(pattern, str(time_str))

    if match:
        year, month, day, hour, minute = match.groups()
        try:
            return datetime(int(year), int(month), int(day), int(hour), int(minute))
        except ValueError:
            return None
    return None

def get_well_prediction_info(well_name: str, prediction_df: pd.DataFrame) -> dict:
    """获取指定井的预测信息"""
    if prediction_df.empty:
        return {}

    # 从文件名中提取井名（去掉"实时数据"后缀）
    clean_well_name = well_name.replace('实时数据', '').strip()

    # 查找匹配的井名 - 先精确匹配，再模糊匹配
    matching_rows = prediction_df[prediction_df['井名'] == clean_well_name]

    if matching_rows.empty:
        # 如果精确匹配失败，尝试模糊匹配
        matching_rows = prediction_df[prediction_df['井名'].str.contains(clean_well_name, na=False, regex=False)]

    if matching_rows.empty:
        return {}

    row = matching_rows.iloc[0]

    return {
        'algo1_ranges': parse_time_ranges(str(row['算法1'])),
        'algo2_ranges': parse_time_ranges(str(row['算法2'])),
        'actual_time': parse_actual_time(str(row['实际卡钻时间']))
    }

def generate_trend_plot(
    csv_path: str,
    output_folder: str,
    time_range: str = None,
    figsize: tuple = (18, 33),
    dpi: int = 300,
    subplot_spacing: float = 2.0,
    prediction_csv_path: str = "原始结果.csv"
):
    """
    :param csv_path: CSV文件路径
    :param output_folder: 图片输出目录
    :param time_range: 时间范围字符串（用于标题）
    :param figsize: 图表尺寸
    :param dpi: 图片分辨率
    :param subplot_spacing: 子图之间的垂直间距，数值越大间距越大（默认2.0）
    :param prediction_csv_path: 预测结果CSV文件路径
    """
    # ========== 参数校验 ==========
    csv_file = Path(csv_path)
    if not csv_file.exists():
        raise FileNotFoundError(f"CSV文件不存在: {csv_path}")
    
    name_map = {
    # 原有特征
    'DEP': '井深',
    'BITDEP': '钻头位置',
    'HOKHEI': '大钩负荷',
    'HKLD': '大钩高度',
    'RPM': '转速',
    'TOR': '扭矩',
    
    # 新增特征
    'SPP': '泵压',
}

    # ========== 配置输出路径 ==========
    output_dir = Path(output_folder)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成图片文件名（与CSV文件名保持一致）
    img_name = f"{csv_file.stem}.png"
    img_path = output_dir / img_name

    # ========== 数据准备 ==========
    try:
        df = pd.read_csv(csv_file)
        df['date'] = pd.to_datetime(df['date'])
    except Exception as e:
        raise ValueError(f"CSV文件读取失败: {str(e)}")

    # ========== 加载预测数据 ==========
    prediction_df = load_prediction_data(prediction_csv_path)
    well_name = csv_file.stem  # 从文件名提取井名
    prediction_info = get_well_prediction_info(well_name, prediction_df)

    # 打印预测信息状态
    clean_well_name = well_name.replace('实时数据', '').strip()
    if prediction_info:
        algo1_count = len(prediction_info.get('algo1_ranges', []))
        algo2_count = len(prediction_info.get('algo2_ranges', []))
        has_actual = prediction_info.get('actual_time') is not None
        print(f"  📊 预测数据({clean_well_name}): 算法1({algo1_count}个窗口), 算法2({algo2_count}个窗口), 实际卡钻({'是' if has_actual else '否'})")
    else:
        print(f"  ⚠️ 未找到井 {clean_well_name} 的预测数据")

    # ========== 图表配置 ==========
    plt.style.use('seaborn-v0_8-colorblind')

    # ========== 重新设置字体大小（在样式之后） ==========
    plt.rcParams['font.size'] = 18          # 基础字体大小
    plt.rcParams['axes.titlesize'] = 14     # 子图标题字体大小
    plt.rcParams['axes.labelsize'] = 12     # 轴标签字体大小
    plt.rcParams['xtick.labelsize'] = 16    # X轴刻度标签字体大小
    plt.rcParams['ytick.labelsize'] = 12    # Y轴刻度标签字体大小
    plt.rcParams['legend.fontsize'] = 16    # 图例字体大小
    plt.rcParams['figure.titlesize'] = 20   # 主标题字体大小

    features = [
        'DEP', 'BITDEP', 'HOKHEI', 'HKLD', 'RPM', 'TOR', 'SPP'
    ]
    
    # ========== 创建图表 ==========
    fig, axes = plt.subplots(len(features), 1, figsize=figsize, sharex=True)

    # 绘制每个特征
    colors = plt.get_cmap('tab10')
    for i, col in enumerate(features):
        axes[i].plot(df['date'], df[col], color=colors(i%10), label=name_map[col], linewidth=1.5)

        # ========== 添加预测窗口可视化 ==========
        if prediction_info:
            # 绘制算法1预测窗口（浅蓝色阴影）
            algo1_ranges = prediction_info.get('algo1_ranges', [])
            for j, (start, end) in enumerate(algo1_ranges):
                label = '算法1预测窗口' if i == 0 and j == 0 else ""
                axes[i].axvspan(start, end, alpha=0.25, color='#6B73FF', label=label)

            # 绘制算法2预测窗口（浅绿色阴影）
            algo2_ranges = prediction_info.get('algo2_ranges', [])
            for j, (start, end) in enumerate(algo2_ranges):
                label = '算法2预测窗口' if i == 0 and j == 0 else ""
                axes[i].axvspan(start, end, alpha=0.25, color='#4CAF50', label=label)

            # 绘制实际卡钻时间（红色虚线）
            actual_time = prediction_info.get('actual_time')
            if actual_time:
                label = '实际卡钻时间' if i == 0 else ""
                axes[i].axvline(actual_time, color='red', linestyle=':', linewidth=2, alpha=0.9, label=label)

        # 设置Y轴标签（带字体大小）
        axes[i].set_ylabel(name_map[col], rotation=0, labelpad=20, ha='right', fontsize=13, fontweight='bold')

        # 设置图例（带字体大小）
        axes[i].legend(loc='best', frameon=True, fontsize=11)

        # 格式化Y轴
        axes[i].yaxis.set_major_formatter(ScalarFormatter(useOffset=False))
        axes[i].ticklabel_format(style='plain', axis='y')

        # 设置刻度标签字体大小
        axes[i].tick_params(axis='both', which='major', labelsize=10)
        axes[i].tick_params(axis='both', which='minor', labelsize=9)

    # ========== 时间轴格式设置 ==========
    for ax in axes:
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        ax.xaxis.set_minor_locator(mdates.HourLocator(interval=1))

        # 设置X轴标签字体大小
        ax.tick_params(axis='x', labelsize=10)

    # 调整底部标签角度和字体大小
    axes[-1].tick_params(axis='x', rotation=0, labelsize=11)

    # 设置X轴标题（如果需要）
#    axes[-1].set_xlabel('时间', fontsize=12, fontweight='bold')

    # ========== 标题和布局 ==========
    title = f'{csv_file.stem} 参数趋势'
    if time_range:
        title += f' ({time_range})'

    # 设置主标题（带字体大小和样式）
    fig.suptitle(title, y=0.98, fontsize=18, fontweight='bold', ha='center')

    # ========== 调整子图间距 ==========
    # 使用 tight_layout 调整垂直间距
    plt.tight_layout(h_pad=subplot_spacing)  # h_pad控制子图之间的垂直间距

    # 如果需要更精确的控制，可以使用 subplots_adjust（注释掉上面的tight_layout）
    # plt.subplots_adjust(
    #     left=0.1,      # 左边距
    #     right=0.95,    # 右边距
    #     top=0.95,      # 顶部边距
    #     bottom=0.05,   # 底部边距
    #     hspace=subplot_spacing * 0.2  # 子图之间的垂直间距
    # )

    # ========== 保存输出 ==========
    plt.savefig(img_path, bbox_inches='tight', dpi=dpi)
    plt.close()
    print(f"✅ 趋势图生成成功: {img_path}")
    return str(img_path)


def batch_process_csv_files(
    input_folder: str = ".",
    output_folder: str = "charts",
    figsize: tuple = (22, 25),
    dpi: int = 200,
    subplot_spacing: float = 2.0,
    prediction_csv_path: str = "原始结果.csv"
):
    """
    批量处理目录中的所有CSV文件，生成趋势图

    :param input_folder: CSV文件所在目录，默认为当前目录
    :param output_folder: 图片输出目录，默认为 "charts"
    :param figsize: 图表尺寸
    :param dpi: 图片分辨率
    :param subplot_spacing: 子图之间的垂直间距，数值越大间距越大（默认2.0）
    :param prediction_csv_path: 预测结果CSV文件路径
    """
    # ========== 扫描CSV文件 ==========
    input_path = Path(input_folder)
    csv_files = list(input_path.glob("*.csv"))

    if not csv_files:
        print("❌ 未找到任何CSV文件")
        return

    print(f"📁 发现 {len(csv_files)} 个CSV文件")
    print(f"📊 输出目录: {output_folder}")
    print("-" * 50)

    # ========== 创建输出目录 ==========
    output_path = Path(output_folder)
    output_path.mkdir(parents=True, exist_ok=True)

    # ========== 批量处理 ==========
    success_count = 0
    failed_files = []

    for i, csv_file in enumerate(csv_files, 1):
        try:
            print(f"🔄 正在处理 {i}/{len(csv_files)}: {csv_file.name}")

            # 调用原有的处理函数
            generate_trend_plot(
                csv_path=str(csv_file),
                output_folder=output_folder,
                figsize=figsize,
                dpi=dpi,
                subplot_spacing=subplot_spacing,
                prediction_csv_path=prediction_csv_path
            )
            success_count += 1

        except Exception as e:
            error_msg = f"❌ 处理失败 {csv_file.name}: {str(e)}"
            print(error_msg)
            failed_files.append((csv_file.name, str(e)))
            continue

    # ========== 处理结果总结 ==========
    print("-" * 50)
    print(f"📈 处理完成！")
    print(f"✅ 成功处理: {success_count} 个文件")
    print(f"❌ 处理失败: {len(failed_files)} 个文件")

    if failed_files:
        print("\n失败文件详情:")
        for filename, error in failed_files:
            print(f"  • {filename}: {error}")

    print(f"\n📂 所有图表已保存到: {output_path.absolute()}")
    return success_count, failed_files

# ===== 使用示例 =====
if __name__ == "__main__":
    # 批量处理当前目录下的所有CSV文件
    batch_process_csv_files(
        input_folder=".",      # 当前目录
        output_folder="charts", # 输出到 charts 子文件夹
        figsize=(16.5, 11),
        dpi=300,
        subplot_spacing=2.0,   # 子图间距，可调整此值改变间距
        prediction_csv_path="原始结果.csv"  # 预测结果文件
    )

    # 如果需要处理单个文件，可以使用：
    # generate_trend_plot(
    #     csv_path="your_file.csv",
    #     output_folder="charts",
    #     figsize=(22, 25),
    #     dpi=200,
    #     subplot_spacing=2.5,  # 更大的间距
    #     prediction_csv_path="原始结果.csv"  # 预测结果文件
    # )